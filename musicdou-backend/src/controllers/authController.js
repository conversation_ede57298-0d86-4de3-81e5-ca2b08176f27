const jwt = require('jsonwebtoken');
const User = require('../models/User');
const PointRecord = require('../models/PointRecord');

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};

// Register new user
const register = async (req, res) => {
  try {
    const { username, email, password, displayName } = req.body;
    
    // Validation
    if (!username || !email || !password) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'Username, email, and password are required'
      });
    }
    
    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [
        { email: email.toLowerCase() },
        { username: username }
      ]
    });
    
    if (existingUser) {
      const field = existingUser.email === email.toLowerCase() ? 'email' : 'username';
      return res.status(409).json({
        error: 'User already exists',
        message: `A user with this ${field} already exists`
      });
    }
    
    // Create new user (without initial points)
    const userData = {
      username,
      email: email.toLowerCase(),
      password,
      points: 0 // Will be set by point record creation
    };

    if (displayName) {
      userData.profile = { displayName };
    }

    const user = new User(userData);
    await user.save();

    // Create registration point record (this will update user points)
    await PointRecord.createRecord({
      userId: user._id,
      type: 'register',
      points: 100,
      description: 'Registration bonus points'
    });
    
    // Generate token
    const token = generateToken(user._id);
    
    // Remove sensitive data from response
    const userResponse = user.toJSON();
    
    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: userResponse,
        token,
        expiresIn: process.env.JWT_EXPIRES_IN || '7d'
      }
    });
    
  } catch (error) {
    console.error('Registration error:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        error: 'Validation failed',
        message: errors.join(', ')
      });
    }
    
    // Handle duplicate key errors
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return res.status(409).json({
        error: 'Duplicate field',
        message: `${field} already exists`
      });
    }
    
    res.status(500).json({
      error: 'Registration failed',
      message: 'An error occurred during registration'
    });
  }
};

// Login user
const login = async (req, res) => {
  try {
    const { identifier, password } = req.body; // identifier can be email or username
    
    // Validation
    if (!identifier || !password) {
      return res.status(400).json({
        error: 'Missing credentials',
        message: 'Email/username and password are required'
      });
    }
    
    // Find user by email or username
    const user = await User.findByEmailOrUsername(identifier);
    
    if (!user) {
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Invalid email/username or password'
      });
    }
    
    // Check if account is locked
    if (user.isLocked) {
      return res.status(423).json({
        error: 'Account locked',
        message: 'Account is temporarily locked due to too many failed login attempts'
      });
    }
    
    // Check if account is active
    if (!user.isActive) {
      return res.status(403).json({
        error: 'Account disabled',
        message: 'Your account has been disabled. Please contact support.'
      });
    }
    
    // Verify password
    const isPasswordValid = await user.comparePassword(password);
    
    if (!isPasswordValid) {
      // Increment login attempts
      await user.incLoginAttempts();
      
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Invalid email/username or password'
      });
    }
    
    // Reset login attempts on successful login
    if (user.loginAttempts > 0) {
      await user.resetLoginAttempts();
    }
    
    // Update last login time
    await user.updateLastLogin();
    
    // Generate token
    const token = generateToken(user._id);
    
    // Remove sensitive data from response
    const userResponse = user.toJSON();
    
    res.status(200).json({
      success: true,
      message: 'Login successful',
      data: {
        user: userResponse,
        token,
        expiresIn: process.env.JWT_EXPIRES_IN || '7d'
      }
    });
    
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      error: 'Login failed',
      message: 'An error occurred during login'
    });
  }
};

// Get current user profile
const getProfile = async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User profile not found'
      });
    }
    
    res.status(200).json({
      success: true,
      data: {
        user: user.toJSON()
      }
    });
    
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      error: 'Failed to get profile',
      message: 'An error occurred while fetching user profile'
    });
  }
};

// Daily sign-in
const dailySignIn = async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User not found'
      });
    }
    
    const signInResult = user.handleDailySignIn();
    
    if (signInResult.alreadySignedIn) {
      return res.status(200).json({
        success: true,
        message: 'Already signed in today',
        data: {
          alreadySignedIn: true,
          points: user.points,
          consecutiveDays: user.consecutiveSignInDays
        }
      });
    }
    
    // Save user with updated points and sign-in data
    await user.save();
    
    // Create point record
    await PointRecord.createRecord({
      userId: user._id,
      type: 'daily_signin',
      points: signInResult.points,
      description: `Daily sign-in bonus (Day ${signInResult.consecutiveDays})`,
      metadata: {
        consecutiveDays: signInResult.consecutiveDays
      }
    });
    
    res.status(200).json({
      success: true,
      message: 'Daily sign-in successful',
      data: {
        alreadySignedIn: false,
        pointsEarned: signInResult.points,
        totalPoints: user.points,
        consecutiveDays: signInResult.consecutiveDays
      }
    });
    
  } catch (error) {
    console.error('Daily sign-in error:', error);
    res.status(500).json({
      error: 'Sign-in failed',
      message: 'An error occurred during daily sign-in'
    });
  }
};

// Refresh token
const refreshToken = async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);

    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User not found'
      });
    }

    // Generate new token
    const newToken = generateToken(user._id);

    res.status(200).json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        token: newToken,
        expiresIn: process.env.JWT_EXPIRES_IN || '7d'
      }
    });

  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(500).json({
      error: 'Token refresh failed',
      message: 'An error occurred while refreshing token'
    });
  }
};

// Logout (for token blacklist in the future)
const logout = async (req, res) => {
  try {
    // For now, just return success
    // In the future, we can implement token blacklisting
    res.status(200).json({
      success: true,
      message: 'Logout successful'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      error: 'Logout failed',
      message: 'An error occurred during logout'
    });
  }
};

module.exports = {
  register,
  login,
  getProfile,
  dailySignIn,
  refreshToken,
  logout
};
