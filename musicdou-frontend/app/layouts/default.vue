<template>
  <div class="min-h-screen bg-white dark:bg-slate-900 text-gray-900 dark:text-gray-100 transition-colors duration-200">
    <!-- 主要内容区域 -->
    <div class="flex flex-col min-h-screen">
      <!-- 导航栏 -->
      <header class="bg-white dark:bg-slate-800 border-b border-gray-200 dark:border-slate-700 transition-colors duration-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex items-center">
              <h1 class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                🎵 MusicDou
              </h1>
            </div>

            <!-- 主题切换按钮 -->
            <div class="flex items-center space-x-4">
              <button
                @click="toggleTheme"
                class="p-2 rounded-lg bg-gray-100 dark:bg-slate-700 hover:bg-gray-200 dark:hover:bg-slate-600 transition-colors duration-200"
                :title="themeName"
              >
                <span class="w-5 h-5 flex items-center justify-center">
                  {{ colorMode.value === 'dark' ? '🌙' : '☀️' }}
                </span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="flex-1">
        <slot />
      </main>

      <!-- 页脚 -->
      <footer class="bg-white dark:bg-slate-800 border-t border-gray-200 dark:border-slate-700 transition-colors duration-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div class="text-center text-gray-600 dark:text-gray-400">
            <p>&copy; 2025 MusicDou. 现代化音乐分享平台</p>
          </div>
        </div>
      </footer>
    </div>

    <!-- Global Notification Container -->
    <UiNotificationContainer />

    <!-- Global Loading Overlay -->
    <Loading
      v-if="isGlobalLoading"
      type="spinner"
      size="lg"
      text="加载中..."
      fullscreen
    />

    <!-- Global Music Player -->
    <MusicMusicPlayer />
  </div>
</template>

<script setup lang="ts">
// 主题管理
const colorMode = useColorMode()

const toggleTheme = () => {
  colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'
}

const themeName = computed(() => {
  switch (colorMode.value) {
    case 'dark':
      return '深色模式'
    case 'light':
      return '浅色模式'
    default:
      return '跟随系统'
  }
})

// 全局状态
const isGlobalLoading = ref(false)
const globalToast = ref()

// 全局方法
const showGlobalToast = (type: 'success' | 'error' | 'warning' | 'info', message: string, title?: string) => {
  if (globalToast.value) {
    globalToast.value.addToast({
      type,
      message,
      title,
      duration: 3000
    })
  }
}

const setGlobalLoading = (loading: boolean) => {
  isGlobalLoading.value = loading
}

// 提供全局方法
provide('showGlobalToast', showGlobalToast)
provide('setGlobalLoading', setGlobalLoading)

// 监听路由变化，关闭加载状态
const route = useRoute()
watch(() => route.path, () => {
  isGlobalLoading.value = false
})
</script>
