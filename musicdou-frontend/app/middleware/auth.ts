export default defineNuxtRouteMiddleware(async (to) => {
  const authStore = useAuthStore()

  // 在客户端检查认证状态
  if (process.client) {
    // 如果用户未登录，重定向到登录页面
    if (!authStore.isLoggedIn) {
      return navigateTo({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    }
  }

  // 在服务端，我们需要检查cookie中的token
  if (process.server) {
    const token = useCookie('auth-token')
    if (!token.value) {
      return navigateTo({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    }

    // 如果有token但没有用户信息，尝试获取用户信息
    if (!authStore.user) {
      try {
        await authStore.fetchUser()
      } catch (error) {
        // 如果获取用户信息失败，清除token并重定向
        token.value = null
        return navigateTo({
          path: '/login',
          query: { redirect: to.fullPath }
        })
      }
    }
  }
})
