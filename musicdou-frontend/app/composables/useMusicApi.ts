import type { Music, MusicUpload, ApiResponse, PaginatedResponse } from '~/types'

export const useMusicApi = () => {
  const { get, post, put, delete: del, upload } = useApi()

  // 获取音乐列表
  const getMusicList = async (params?: {
    page?: number
    limit?: number
    genre?: string
    language?: string
    quality?: string
    sortBy?: 'latest' | 'popular' | 'trending'
    search?: string
  }): Promise<ApiResponse<PaginatedResponse<Music>>> => {
    return await get('/music', params)
  }

  // 获取音乐详情
  const getMusicById = async (id: string): Promise<ApiResponse<Music>> => {
    return await get(`/music/${id}`)
  }

  // 搜索音乐
  const searchMusic = async (params: {
    q: string
    page?: number
    limit?: number
    genre?: string
    language?: string
    sortBy?: 'relevance' | 'latest' | 'popular'
  }): Promise<ApiResponse<PaginatedResponse<Music>>> => {
    return await get('/music/search', params)
  }

  // 获取热门音乐
  const getTrendingMusic = async (params?: {
    period?: 'day' | 'week' | 'month'
    limit?: number
  }): Promise<ApiResponse<Music[]>> => {
    return await get('/music/trending', params)
  }

  // 获取最新音乐
  const getLatestMusic = async (params?: {
    limit?: number
    genre?: string
  }): Promise<ApiResponse<Music[]>> => {
    return await get('/music/recent', params)
  }

  // 获取热门音乐
  const getPopularMusic = async (params?: {
    limit?: number
    genre?: string
  }): Promise<ApiResponse<Music[]>> => {
    return await get('/music/popular', params)
  }

  // 获取推荐音乐
  const getRecommendedMusic = async (params?: {
    limit?: number
    type?: 'similar' | 'genre' | 'collaborative'
  }): Promise<ApiResponse<Music[]>> => {
    return await get('/music/recommendations', params)
  }

  // 获取发现音乐
  const getDiscoverMusic = async (params?: {
    limit?: number
    genre?: string
  }): Promise<ApiResponse<Music[]>> => {
    return await get('/music/discover', params)
  }

  // 上传音乐
  const uploadMusic = async (
    musicData: MusicUpload,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<Music>> => {
    const formData = new FormData()
    
    // 添加音乐文件
    formData.append('file', musicData.file)
    
    // 添加封面文件（如果有）
    if (musicData.cover) {
      formData.append('cover', musicData.cover)
    }
    
    // 添加其他数据
    Object.entries(musicData).forEach(([key, value]) => {
      if (key !== 'file' && key !== 'cover' && value !== undefined) {
        if (Array.isArray(value)) {
          formData.append(key, JSON.stringify(value))
        } else {
          formData.append(key, String(value))
        }
      }
    })

    return await upload('/music/upload', musicData.file, onProgress)
  }

  // 更新音乐信息
  const updateMusic = async (id: string, musicData: Partial<Music>): Promise<ApiResponse<Music>> => {
    return await put(`/music/${id}`, musicData)
  }

  // 删除音乐
  const deleteMusic = async (id: string): Promise<ApiResponse<{ message: string }>> => {
    return await del(`/music/${id}`)
  }

  // 点赞音乐
  const likeMusic = async (id: string): Promise<ApiResponse<{ isLiked: boolean; likeCount: number }>> => {
    return await post(`/music/${id}/like`)
  }

  // 取消点赞
  const unlikeMusic = async (id: string): Promise<ApiResponse<{ isLiked: boolean; likeCount: number }>> => {
    return await del(`/music/${id}/like`)
  }

  // 分享音乐
  const shareMusic = async (id: string, platform?: string): Promise<ApiResponse<{ shareCount: number; shareUrl: string }>> => {
    return await post(`/music/${id}/share`, { platform })
  }

  // 记录播放
  const recordPlay = async (id: string): Promise<ApiResponse<{ playCount: number }>> => {
    return await post(`/music/${id}/play-behavior`)
  }

  // 获取音乐播放URL
  const getMusicPlayUrl = async (id: string): Promise<ApiResponse<{ url: string }>> => {
    return await get(`/music/${id}/play`)
  }

  // 获取音乐统计
  const getMusicStats = async (id: string): Promise<ApiResponse<{
    playCount: number
    likeCount: number
    shareCount: number
    commentCount: number
    dailyPlays: number[]
    topCountries: Array<{ country: string; count: number }>
  }>> => {
    return await get(`/music/${id}/stats`)
  }

  // 获取用户上传的音乐
  const getUserMusic = async (params?: {
    page?: number
    limit?: number
    status?: 'active' | 'pending' | 'rejected'
  }): Promise<ApiResponse<PaginatedResponse<Music>>> => {
    return await get('/music/my/uploads', params)
  }

  // 获取用户喜欢的音乐
  const getUserLikedMusic = async (params?: {
    page?: number
    limit?: number
  }): Promise<ApiResponse<PaginatedResponse<Music>>> => {
    return await get('/music/liked', params)
  }

  // 获取音乐流派列表
  const getGenres = async (): Promise<ApiResponse<string[]>> => {
    return await get('/music/genres')
  }

  // 获取音乐语言列表
  const getLanguages = async (): Promise<ApiResponse<string[]>> => {
    return await get('/music/languages')
  }

  // 批量操作音乐
  const batchUpdateMusic = async (ids: string[], updates: Partial<Music>): Promise<ApiResponse<{ updated: number }>> => {
    return await put('/music/batch', { ids, updates })
  }

  // 批量删除音乐
  const batchDeleteMusic = async (ids: string[]): Promise<ApiResponse<{ deleted: number }>> => {
    return await del('/music/batch', { ids })
  }

  return {
    getMusicList,
    getMusicById,
    searchMusic,
    getTrendingMusic,
    getLatestMusic,
    getPopularMusic,
    getRecommendedMusic,
    getDiscoverMusic,
    uploadMusic,
    updateMusic,
    deleteMusic,
    likeMusic,
    unlikeMusic,
    shareMusic,
    recordPlay,
    getMusicPlayUrl,
    getMusicStats,
    getUserMusic,
    getUserLikedMusic,
    getGenres,
    getLanguages,
    batchUpdateMusic,
    batchDeleteMusic
  }
}
