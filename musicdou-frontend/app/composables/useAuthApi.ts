import type { User, LoginForm, RegisterForm, ApiResponse } from '~/types'

export const useAuthApi = () => {
  const { get, post, put } = useApi()

  // 用户注册
  const register = async (userData: RegisterForm): Promise<ApiResponse<{ user: User; token: string }>> => {
    return await post('/auth/register', userData)
  }

  // 用户登录
  const login = async (credentials: LoginForm): Promise<ApiResponse<{ user: User; token: string }>> => {
    return await post('/auth/login', credentials)
  }

  // 获取当前用户信息
  const getCurrentUser = async (): Promise<ApiResponse<User>> => {
    return await get('/auth/profile')
  }

  // 更新用户资料
  const updateProfile = async (profileData: Partial<User>): Promise<ApiResponse<User>> => {
    return await put('/auth/profile', profileData)
  }

  // 修改密码
  const changePassword = async (passwordData: {
    currentPassword: string
    newPassword: string
  }): Promise<ApiResponse<{ message: string }>> => {
    return await put('/auth/password', passwordData)
  }

  // 上传头像
  const uploadAvatar = async (file: File): Promise<ApiResponse<{ avatarUrl: string }>> => {
    const { upload } = useApi()
    return await upload('/auth/avatar', file)
  }

  // 每日签到
  const dailyCheckin = async (): Promise<ApiResponse<{ points: number; message: string }>> => {
    return await post('/auth/signin')
  }

  // 获取签到状态
  const getCheckinStatus = async (): Promise<ApiResponse<{ 
    hasCheckedIn: boolean
    consecutiveDays: number
    totalCheckins: number
    nextReward: number
  }>> => {
    return await get('/auth/checkin/status')
  }

  // 用户登出
  const logout = async (): Promise<ApiResponse<{ message: string }>> => {
    return await post('/auth/logout')
  }

  // 刷新token
  const refreshToken = async (): Promise<ApiResponse<{ token: string }>> => {
    return await post('/auth/refresh')
  }

  // 忘记密码
  const forgotPassword = async (email: string): Promise<ApiResponse<{ message: string }>> => {
    return await post('/auth/forgot-password', { email })
  }

  // 重置密码
  const resetPassword = async (resetData: {
    token: string
    newPassword: string
  }): Promise<ApiResponse<{ message: string }>> => {
    return await post('/auth/reset-password', resetData)
  }

  // 验证邮箱
  const verifyEmail = async (token: string): Promise<ApiResponse<{ message: string }>> => {
    return await post('/auth/verify-email', { token })
  }

  // 重发验证邮件
  const resendVerificationEmail = async (): Promise<ApiResponse<{ message: string }>> => {
    return await post('/auth/resend-verification')
  }

  // 删除账户
  const deleteAccount = async (password: string): Promise<ApiResponse<{ message: string }>> => {
    return await post('/auth/delete-account', { password })
  }

  return {
    register,
    login,
    getCurrentUser,
    updateProfile,
    changePassword,
    uploadAvatar,
    dailyCheckin,
    getCheckinStatus,
    logout,
    refreshToken,
    forgotPassword,
    resetPassword,
    verifyEmail,
    resendVerificationEmail,
    deleteAccount
  }
}
