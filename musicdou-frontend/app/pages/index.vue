<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700">
    <!-- 主页内容 - 仅登录后显示 -->
    <div class="container mx-auto px-4 py-12">
      <!-- 欢迎区域 -->
      <div class="text-center mb-12">
        <h1 class="text-5xl font-bold text-white mb-4">
          🎵 欢迎来到 MusicDou
        </h1>
        <p class="text-xl text-white/80 mb-8">
          {{ user?.username || '用户' }}，开始你的音乐之旅吧
        </p>
      </div>

      <!-- 功能卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        <!-- 发现音乐 -->
        <NuxtLink 
          to="/discover" 
          class="bg-white/10 backdrop-blur-lg rounded-2xl p-6 hover:bg-white/20 transition-all duration-300 cursor-pointer block"
        >
          <div class="text-4xl mb-4">🎧</div>
          <h3 class="text-xl font-semibold text-white mb-2">发现音乐</h3>
          <p class="text-white/70">探索最新最热的音乐作品</p>
        </NuxtLink>

        <!-- 我的歌单 -->
        <NuxtLink 
          to="/playlists" 
          class="bg-white/10 backdrop-blur-lg rounded-2xl p-6 hover:bg-white/20 transition-all duration-300 cursor-pointer block"
        >
          <div class="text-4xl mb-4">📝</div>
          <h3 class="text-xl font-semibold text-white mb-2">我的歌单</h3>
          <p class="text-white/70">管理你的个人音乐收藏</p>
        </NuxtLink>

        <!-- 音乐社区 -->
        <NuxtLink 
          to="/social" 
          class="bg-white/10 backdrop-blur-lg rounded-2xl p-6 hover:bg-white/20 transition-all duration-300 cursor-pointer block"
        >
          <div class="text-4xl mb-4">👥</div>
          <h3 class="text-xl font-semibold text-white mb-2">音乐社区</h3>
          <p class="text-white/70">与音乐爱好者分享交流</p>
        </NuxtLink>

        <!-- 个人资料 -->
        <NuxtLink 
          to="/profile" 
          class="bg-white/10 backdrop-blur-lg rounded-2xl p-6 hover:bg-white/20 transition-all duration-300 cursor-pointer block"
        >
          <div class="text-4xl mb-4">👤</div>
          <h3 class="text-xl font-semibold text-white mb-2">个人资料</h3>
          <p class="text-white/70">查看和编辑个人信息</p>
        </NuxtLink>

        <!-- 音乐播放器 -->
        <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-6 hover:bg-white/20 transition-all duration-300 cursor-pointer">
          <div class="text-4xl mb-4">🎵</div>
          <h3 class="text-xl font-semibold text-white mb-2">音乐播放器</h3>
          <p class="text-white/70">享受高品质音乐播放</p>
        </div>

        <!-- 设置 -->
        <NuxtLink 
          to="/settings" 
          class="bg-white/10 backdrop-blur-lg rounded-2xl p-6 hover:bg-white/20 transition-all duration-300 cursor-pointer block"
        >
          <div class="text-4xl mb-4">⚙️</div>
          <h3 class="text-xl font-semibold text-white mb-2">设置</h3>
          <p class="text-white/70">个性化你的音乐体验</p>
        </NuxtLink>
      </div>

      <!-- 快速操作 -->
      <div class="text-center">
        <div class="space-x-4">
          <NuxtLink 
            to="/discover" 
            class="inline-block bg-white text-purple-600 px-8 py-3 rounded-full font-semibold hover:bg-white/90 transition-colors"
          >
            开始探索
          </NuxtLink>
          <button
            @click="handleLogout"
            class="inline-block bg-white/20 text-white px-8 py-3 rounded-full font-semibold hover:bg-white/30 transition-colors"
          >
            退出登录
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 使用认证状态
const { user, isLoggedIn, logout } = useAuth()

// 检查认证状态，未登录则跳转到登录页
onMounted(async () => {
  // 初始化认证状态
  const { initAuth } = useAuth()
  await initAuth()

  if (!isLoggedIn.value) {
    navigateTo('/login')
  }
})

// 监听认证状态变化
watch(isLoggedIn, (newValue) => {
  if (!newValue) {
    navigateTo('/login')
  }
})

// 处理退出登录
const handleLogout = async () => {
  await logout()
  // logout函数会自动跳转到登录页面
}

// 页面元数据
useHead({
  title: 'MusicDou - 音乐主页',
  meta: [
    { name: 'description', content: '欢迎来到MusicDou，开始你的音乐之旅。' }
  ]
})
</script>
